import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  TextField,
  InputAdornment,
  IconButton,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Chip,
  Button
} from '@mui/material';
import {
  Search as SearchIcon,
  Clear as ClearIcon,
  Cancel as CancelIcon,
  CheckBox as CheckBoxIcon,
  CheckBoxOutlineBlank as CheckBoxOutlineBlankIcon
} from '@mui/icons-material';

/**
 * Componente per il filtraggio intelligente dei cavi
 * Implementa un sistema di ricerca simile a quello delle bobine con supporto per termini multipli separati da virgola
 * 
 * @param {Object} props - Proprietà del componente
 * @param {Array} props.cavi - Lista completa dei cavi
 * @param {Function} props.onFilteredDataChange - Callback chiamata quando i dati filtrati cambiano
 * @param {boolean} props.loading - Indica se i dati sono in caricamento
 */
const SmartCaviFilter = ({
  cavi = [],
  onFilteredDataChange = null,
  loading = false,
  selectionEnabled = false,
  onSelectionToggle = null
}) => {
  const [searchText, setSearchText] = useState('');
  const [searchType, setSearchType] = useState('contains'); // 'contains' o 'equals'

  /**
   * Normalizza una stringa per la ricerca (lowercase, trim)
   */
  const normalizeString = (str) => {
    return String(str || '').toLowerCase().trim();
  };

  /**
   * Mappa di sinonimi e alias per ricerca intelligente
   */
  const synonymMap = {
    // Bobina vuota
    'vuota': 'bobina_vuota',
    'empty': 'bobina_vuota',
    'senza bobina': 'bobina_vuota',
    'no bobina': 'bobina_vuota',

    // Utility
    'mt': ['mt', 'media tensione'],
    'bt': ['bt', 'bassa tensione'],
    'tlc': ['tlc', 'telecomunicazioni'],

    // Stati
    'installato': 'installato',
    'posato': 'installato',
    'da installare': 'da_installare',
    'da posare': 'da_installare',
    'in corso': 'in_corso',

    // Sistemi
    'principale': 'principale',
    'main': 'principale',
    'ausiliario': 'ausiliario',
    'aux': 'ausiliario',
    'emergency': 'emergency',
    'emergenza': 'emergency'
  };

  /**
   * Espande un termine di ricerca con sinonimi
   */
  const expandSearchTerm = (term) => {
    const normalizedTerm = normalizeString(term);
    const synonyms = synonymMap[normalizedTerm];

    if (synonyms) {
      if (Array.isArray(synonyms)) {
        return synonyms;
      } else {
        return [normalizedTerm, synonyms];
      }
    }

    return [normalizedTerm];
  };

  /**
   * Calcola la distanza di Levenshtein per ricerca fuzzy
   */
  const levenshteinDistance = (str1, str2) => {
    const matrix = [];

    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }

    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }

    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          );
        }
      }
    }

    return matrix[str2.length][str1.length];
  };

  /**
   * Verifica se due stringhe sono simili (ricerca fuzzy)
   */
  const isFuzzyMatch = (term, target, threshold = 2) => {
    if (term.length < 3) return false; // Non applicare fuzzy per termini troppo corti

    const distance = levenshteinDistance(term, target);
    const maxLength = Math.max(term.length, target.length);

    // Permetti errori in base alla lunghezza del termine
    const allowedErrors = Math.min(threshold, Math.floor(maxLength * 0.3));

    return distance <= allowedErrors;
  };

  /**
   * Verifica se un termine corrisponde a un pattern con wildcards
   */
  const matchesWildcard = (pattern, text) => {
    // Converte pattern con * e ? in regex
    const regexPattern = pattern
      .replace(/[.*+?^${}()|[\]\\]/g, '\\$&') // Escape caratteri speciali
      .replace(/\\\*/g, '.*') // * diventa .*
      .replace(/\\\?/g, '.'); // ? diventa .

    const regex = new RegExp(`^${regexPattern}$`, 'i');
    return regex.test(text);
  };

  /**
   * Verifica se un termine è un range numerico (es: "240-400", ">100", "<50")
   */
  const parseNumericRange = (term) => {
    // Range: "240-400"
    const rangeMatch = term.match(/^(\d+(?:\.\d+)?)-(\d+(?:\.\d+)?)$/);
    if (rangeMatch) {
      return {
        type: 'range',
        min: parseFloat(rangeMatch[1]),
        max: parseFloat(rangeMatch[2])
      };
    }

    // Maggiore di: ">100"
    const gtMatch = term.match(/^>(\d+(?:\.\d+)?)$/);
    if (gtMatch) {
      return {
        type: 'greater',
        value: parseFloat(gtMatch[1])
      };
    }

    // Minore di: "<50"
    const ltMatch = term.match(/^<(\d+(?:\.\d+)?)$/);
    if (ltMatch) {
      return {
        type: 'less',
        value: parseFloat(ltMatch[1])
      };
    }

    return null;
  };

  /**
   * Verifica se un termine numerico corrisponde a un valore
   */
  const matchesNumericTerm = (term, value) => {
    const numericTerm = parseFloat(term);
    const numericValue = parseFloat(String(value || '0'));
    
    if (isNaN(numericTerm) || isNaN(numericValue)) {
      return false;
    }
    
    return numericValue === numericTerm;
  };

  /**
   * Estrae informazioni dall'ID del cavo per facilitare la ricerca
   */
  const getCavoInfo = (idCavo) => {
    if (!idCavo) return { number: '', suffix: '', full: '' };

    // Estrae il numero finale (es. "CANT_001_C001" -> "001")
    const numberMatch = idCavo.match(/_C(\d+)$/);
    const number = numberMatch ? numberMatch[1] : '';

    // Estrae la parte finale completa (es. "CANT_001_C001" -> "C001")
    const suffixMatch = idCavo.match(/(C\d+)$/);
    const suffix = suffixMatch ? suffixMatch[1] : '';

    return {
      number: number,
      suffix: suffix,
      full: idCavo
    };
  };

  /**
   * Verifica se un cavo corrisponde a un termine di ricerca (versione intelligente)
   */
  const cavoMatchesTerm = (cavo, term, isExactMatch = false) => {
    const termStr = normalizeString(term);
    const isNumericTerm = !isNaN(termStr) && !isNaN(parseFloat(termStr));

    // Verifica se è un pattern con wildcards
    const hasWildcards = termStr.includes('*') || termStr.includes('?');

    // Verifica se è un range numerico
    const numericRange = parseNumericRange(termStr);

    // Espandi il termine con sinonimi
    const expandedTerms = expandSearchTerm(termStr);

    // Campi da cercare
    const cavoInfo = getCavoInfo(cavo.id_cavo);
    const cavoId = normalizeString(cavoInfo.full);
    const cavoNumber = normalizeString(cavoInfo.number);
    const cavoSuffix = normalizeString(cavoInfo.suffix);
    const tipologia = normalizeString(cavo.tipologia);
    const sezione = normalizeString(cavo.sezione);
    const utility = normalizeString(cavo.utility);
    const sistema = normalizeString(cavo.sistema);
    const ubicazionePartenza = normalizeString(cavo.ubicazione_partenza);
    const ubicazioneArrivo = normalizeString(cavo.ubicazione_arrivo);
    const utenzaPartenza = normalizeString(cavo.utenza_partenza);
    const utenzaArrivo = normalizeString(cavo.utenza_arrivo);

    // Aggiungi anche il campo bobina per la ricerca
    const bobina = normalizeString(cavo.id_bobina);
    const bobinaDisplay = cavo.id_bobina === 'BOBINA_VUOTA' ? 'bobina vuota' :
                         cavo.id_bobina === null ? '' :
                         normalizeString(cavo.id_bobina);

    // Array di tutti i campi di testo da cercare
    const textFields = [
      cavoId, cavoNumber, cavoSuffix, tipologia, sezione, utility, sistema,
      ubicazionePartenza, ubicazioneArrivo, utenzaPartenza, utenzaArrivo,
      bobina, bobinaDisplay
    ];

    // Array di campi numerici per range
    const numericFields = [
      { value: cavo.metri_teorici, name: 'metri_teorici' },
      { value: cavo.metratura_reale, name: 'metratura_reale' },
      { value: parseFloat(cavo.sezione), name: 'sezione' }
    ];

    // Gestione range numerici
    if (numericRange) {
      return numericFields.some(field => {
        const value = parseFloat(field.value);
        if (isNaN(value)) return false;

        switch (numericRange.type) {
          case 'range':
            return value >= numericRange.min && value <= numericRange.max;
          case 'greater':
            return value > numericRange.value;
          case 'less':
            return value < numericRange.value;
          default:
            return false;
        }
      });
    }

    // Gestione pattern con wildcards
    if (hasWildcards) {
      return textFields.some(field => matchesWildcard(termStr, field));
    }

    // Ricerca con sinonimi espansi
    const matchesAnyTerm = (fieldValue) => {
      return expandedTerms.some(expandedTerm => {
        if (isExactMatch) {
          return fieldValue === expandedTerm;
        } else {
          // Ricerca normale
          if (fieldValue.includes(expandedTerm)) {
            return true;
          }

          // Ricerca fuzzy per termini più lunghi
          if (expandedTerm.length >= 4) {
            return isFuzzyMatch(expandedTerm, fieldValue);
          }

          return false;
        }
      });
    };

    // Applica la ricerca intelligente su tutti i campi
    return textFields.some(field => matchesAnyTerm(field));
  };

  /**
   * Applica il filtro ai cavi
   */
  const applyFilter = useCallback(() => {
    if (!searchText.trim()) {
      // Se non c'è testo di ricerca, mostra tutti i cavi
      if (onFilteredDataChange) {
        onFilteredDataChange(cavi);
      }
      return;
    }

    // Dividi il testo di ricerca in termini separati da virgola
    const searchTerms = searchText.split(',')
      .map(term => term.trim())
      .filter(term => term.length > 0);

    console.log('SmartCaviFilter - Ricerca:', {
      searchText,
      searchType,
      searchTerms,
      totalCavi: cavi.length
    });

    let filtered = [];

    if (searchType === 'equals') {
      // Per la ricerca esatta con termini multipli, tutti i termini devono corrispondere (AND)
      if (searchTerms.length === 1) {
        // Singolo termine: ricerca esatta
        filtered = cavi.filter(cavo => {
          const matches = cavoMatchesTerm(cavo, searchTerms[0], true);
          if (matches) {
            console.log('SmartCaviFilter - Match trovato:', {
              cavo: cavo.id_cavo,
              term: searchTerms[0],
              cavoInfo: getCavoInfo(cavo.id_cavo)
            });
          }
          return matches;
        });
      } else {
        // Termini multipli: tutti devono corrispondere
        filtered = cavi.filter(cavo =>
          searchTerms.every(term => cavoMatchesTerm(cavo, term, true))
        );
      }
    } else {
      // Per la ricerca con 'contains', almeno un termine deve corrispondere (OR)
      filtered = cavi.filter(cavo =>
        searchTerms.some(term => cavoMatchesTerm(cavo, term, false))
      );
    }

    console.log('SmartCaviFilter - Risultati:', {
      filteredCount: filtered.length,
      filteredIds: filtered.map(c => c.id_cavo)
    });

    if (onFilteredDataChange) {
      onFilteredDataChange(filtered);
    }
  }, [searchText, searchType, cavi, onFilteredDataChange]);

  /**
   * Gestisce il cambio del testo di ricerca
   */
  const handleSearchTextChange = (event) => {
    setSearchText(event.target.value);
  };

  /**
   * Pulisce il filtro
   */
  const clearFilter = () => {
    setSearchText('');
    setSearchType('contains');
  };

  /**
   * Conta i termini di ricerca
   */
  const getSearchTermsCount = () => {
    if (!searchText.trim()) return 0;
    return searchText.split(',').map(term => term.trim()).filter(term => term.length > 0).length;
  };

  // Applica il filtro quando cambiano i parametri di ricerca o i dati
  useEffect(() => {
    applyFilter();
  }, [applyFilter]);

  return (
    <Box sx={{ mb: 3 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', gap: 2, mb: 2 }}>
        {/* Sezione sinistra: ricerca e filtri */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          {/* Campo di ricerca principale - accorciato */}
          <TextField
            size="small"
            label="Ricerca intelligente cavi"
            variant="outlined"
            value={searchText}
            onChange={handleSearchTextChange}
            placeholder="Ricerca: ID, tipologia, utility, bobina..."
            disabled={loading}
            sx={{ width: '400px' }} // Larghezza fissa invece di flexGrow
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon fontSize="small" />
                </InputAdornment>
              ),
              endAdornment: searchText ? (
                <InputAdornment position="end">
                  <IconButton
                    size="small"
                    aria-label="clear search"
                    onClick={clearFilter}
                    edge="end"
                  >
                    <CancelIcon fontSize="small" />
                  </IconButton>
                </InputAdornment>
              ) : null
            }}
          />

          {/* Dropdown per il tipo di ricerca */}
          <FormControl size="small" sx={{ minWidth: '140px' }}>
            <InputLabel id="search-type-label">Tipo ricerca</InputLabel>
            <Select
              labelId="search-type-label"
              value={searchType}
              label="Tipo ricerca"
              onChange={(e) => setSearchType(e.target.value)}
              disabled={loading}
            >
              <MenuItem value="contains">Contiene</MenuItem>
              <MenuItem value="equals">Uguale a</MenuItem>
            </Select>
          </FormControl>

          {/* Pulsante per pulire tutti i filtri */}
          {searchText && (
            <Button
              variant="outlined"
              size="small"
              startIcon={<ClearIcon />}
              onClick={clearFilter}
              disabled={loading}
            >
              Pulisci
            </Button>
          )}
        </Box>

        {/* Sezione destra: pulsante selezione */}
        {onSelectionToggle && (
          <Button
            variant={selectionEnabled ? "contained" : "outlined"}
            color="primary"
            size="small"
            onClick={onSelectionToggle}
            startIcon={selectionEnabled ? <CheckBoxIcon /> : <CheckBoxOutlineBlankIcon />}
            sx={{
              fontWeight: 'normal',
              '&:hover': {
                fontWeight: 'bold'
              }
            }}
          >
            {selectionEnabled ? 'Disabilita Selezione' : 'Abilita Selezione'}
          </Button>
        )}
      </Box>

      {/* Informazioni sui risultati */}
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flexWrap: 'wrap' }}>
        {/* Chip con informazioni sui termini di ricerca */}
        {searchText && (
          <Chip
            size="small"
            label={`${getSearchTermsCount()} termine${getSearchTermsCount() > 1 ? 'i' : ''} di ricerca`}
            color="primary"
            variant="outlined"
          />
        )}

        {/* Chip con tipo di ricerca attivo */}
        {searchText && (
          <Chip
            size="small"
            label={searchType === 'contains' ? 'Ricerca per contenuto' : 'Ricerca esatta'}
            color="secondary"
            variant="outlined"
          />
        )}
      </Box>

      {/* Suggerimenti per l'uso */}
      {!searchText && (
        <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
          💡 <strong>Ricerca super intelligente:</strong>
          • <strong>Sinonimi:</strong> "vuota", "mt", "bt", "posato"
          • <strong>Pattern:</strong> "C*" (inizia con C), "*240*" (contiene 240)
          • <strong>Range:</strong> ">100" (metri > 100), "240-400" (sezione 240-400mm²)
          • <strong>Fuzzy:</strong> "Lightin" trova "Lighting"
          • <strong>Virgole:</strong> "C001,vuota" (termini multipli)
        </Typography>
      )}
    </Box>
  );
};

export default SmartCaviFilter;
